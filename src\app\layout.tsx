import 'react-toastify/dist/ReactToastify.css';
import type { Metadata } from "next";
import { <PERSON>ei<PERSON>, <PERSON><PERSON>st_Mono } from "next/font/google";
import "./globals.css";
import Navbar from "@/components/NavBar";
import { ToastContainer } from 'react-toastify';
import Footer from "@/components/Footer";
import { AuthProvider } from "@/contexts/AuthContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Mega Mall",
  description: "Your one-stop destination for quality products at unbeatable prices.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <AuthProvider>
          <Navbar />
          {children}

          <ToastContainer
          position="top-right"
          autoClose={3000}
          hideProgressBar={false}
          newestOnTop
          closeOnClick
          pauseOnHover
          theme="light"
          />
          <Footer />
        </AuthProvider>
      </body>
    </html>
  );
}
