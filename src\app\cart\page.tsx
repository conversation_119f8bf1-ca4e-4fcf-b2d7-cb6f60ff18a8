"use client";

import { useRouter } from "next/navigation";
import { ArrowLeft, ShoppingBag } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { LoadingPage } from "@/components/ui/loading";
import { useCart } from "@/contexts/CartContext";
import ProtectedRoute from "@/components/ProtectedRoute";

import { CartItem, CartSummary, EmptyCart } from "@/components/cart";
import CartErrorBoundary from "@/components/cart/CartErrorBoundary";

export default function CartPage() {
  const { cart, isLoading, isUpdating, updateQuantity, removeFromCart } = useCart();
  const router = useRouter();

  const handleQuantityUpdate = async (productId: string, newQuantity: number) => {
    await updateQuantity(productId, newQuantity);
  };

  const handleRemoveItem = async (productId: string) => {
    await removeFromCart(productId);
  };

  const handleContinueShopping = () => {
    router.push("/");
  };

  const handleProceedToCheckout = () => {
    // TODO: Implement checkout page navigation
    router.push("/checkout");
  };

  if (isLoading) {
    return <LoadingPage message="Loading your cart..." />;
  }

  return (
    <ProtectedRoute>
      <CartErrorBoundary>
        <div className="min-h-screen bg-gray-50 py-8">
        <div className="container mx-auto px-4 max-w-7xl">
          {/* Header */}
          <div className="mb-8">
            <Button
              variant="ghost"
              onClick={handleContinueShopping}
              className="mb-4 text-gray-600 hover:text-gray-800"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Continue Shopping
            </Button>
            
            <div className="flex items-center gap-3">
              <ShoppingBag className="w-8 h-8 text-primary" />
              <h1 className="text-3xl font-bold text-gray-900">Shopping Cart</h1>
              {cart && cart.items.length > 0 && (
                <span className="text-lg text-gray-600">
                  ({cart.items.length} {cart.items.length === 1 ? 'item' : 'items'})
                </span>
              )}
            </div>
          </div>

          {/* Cart Content */}
          {!cart || cart.items.length === 0 ? (
            <EmptyCart onContinueShopping={handleContinueShopping} />
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Cart Items */}
              <div className="lg:col-span-2 space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Cart Items</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {cart.items.map((item) => (
                      <CartItem
                        key={item._id}
                        item={item}
                        onQuantityUpdate={handleQuantityUpdate}
                        onRemove={handleRemoveItem}
                        isUpdating={isUpdating}
                      />
                    ))}
                  </CardContent>
                </Card>
              </div>

              {/* Cart Summary */}
              <div className="lg:col-span-1">
                <CartSummary
                  cart={cart}
                  onProceedToCheckout={handleProceedToCheckout}
                  isUpdating={isUpdating}
                />
              </div>
            </div>
          )}
        </div>
        </div>
      </CartErrorBoundary>
    </ProtectedRoute>
  );
}
